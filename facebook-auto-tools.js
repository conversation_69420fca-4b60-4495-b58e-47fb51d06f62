// ==UserScript==
// @name         Facebook Auto Tools
// @namespace    http://tampermonkey.net/
// @version      0.2
// @description  Facebook自动工具集 - 包含自动点赞和页面快速切换功能
// <AUTHOR> Name
// @match        https://www.facebook.com/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // =============== 共享状态和变量 ===============

    // 自动点赞状态
    const storedState = getStoredState();
    let isRunning = storedState.isRunning;
    let currentOriginalUrl = storedState.originalUrl;
    let currentFinalUrl = storedState.finalUrl;

    // 页面切换状态
    let publicPages = [];
    let currentPageIndex = 0;
    let savedPageNames = JSON.parse(localStorage.getItem('fb_saved_page_names') || '[]');

    // 点赞列表 - 全局共享的待处理链接
    const targetPosts = JSON.parse(localStorage.getItem('fb_auto_like_posts_global') || '[]');
    // 当前账号的已点赞记录 - 初始化时先设为空数组，稍后在页面加载时正确初始化
    let likedPosts = [];

    // =============== 共享工具函数 ===============

    // 获取当前账号名称
    function getCurrentAccountName() {
        // 尝试从页面标题获取账号名
        const title = document.title;
        if (title && title !== 'Facebook') {
            return title.split(' | ')[0] || title.split(' - ')[0] || 'unknown';
        }

        // 尝试从URL获取账号信息
        const url = window.location.href;
        if (url.includes('/pages/')) {
            const match = url.match(/\/pages\/([^\/]+)/);
            if (match) return decodeURIComponent(match[1]);
        }

        // 默认返回个人账号
        return 'personal_account';
    }

    // 获取当前账号的已点赞记录
    function getCurrentAccountLikedPosts() {
        const currentAccount = getCurrentAccountName();
        const allAccountsLiked = JSON.parse(localStorage.getItem('fb_liked_posts_by_account') || '{}');
        return allAccountsLiked[currentAccount] || [];
    }

    // 保存当前账号的已点赞记录
    function saveCurrentAccountLikedPosts(likedPostsArray) {
        const currentAccount = getCurrentAccountName();
        const allAccountsLiked = JSON.parse(localStorage.getItem('fb_liked_posts_by_account') || '{}');
        allAccountsLiked[currentAccount] = likedPostsArray;
        localStorage.setItem('fb_liked_posts_by_account', JSON.stringify(allAccountsLiked));
        logUserAction(`保存账号 ${currentAccount} 的点赞记录: ${likedPostsArray.length} 个`);
    }

    // 刷新当前账号的点赞记录（切换账号时调用）
    function refreshCurrentAccountData() {
        const currentAccount = getCurrentAccountName();

        // 重新加载全局待处理链接
        const storedTargetPosts = JSON.parse(localStorage.getItem('fb_auto_like_posts_global') || '[]');
        targetPosts.length = 0;
        storedTargetPosts.forEach(url => targetPosts.push(url));

        // 重新加载当前账号的已点赞记录
        likedPosts = getCurrentAccountLikedPosts();

        logUserAction(`切换到账号: ${currentAccount}，全局待处理: ${targetPosts.length}，已点赞数量: ${likedPosts.length}`);

        // 更新UI显示
        updatePendingList();
        updateAccountInfo();
    }

    // 存储当前状态到 localStorage
    function saveCurrentState(isRunning, originalUrl = '', finalUrl = '') {
        localStorage.setItem('fb_auto_like_state', JSON.stringify({
            isRunning: isRunning,
            originalUrl: originalUrl,
            finalUrl: finalUrl,
            timestamp: new Date().getTime()
        }));
    }

    // 从 localStorage 获取状态
    function getStoredState() {
        const state = localStorage.getItem('fb_auto_like_state');
        if (state) {
            const parsedState = JSON.parse(state);
            if (new Date().getTime() - parsedState.timestamp < 300000) {
                return parsedState;
            }
        }
        return { isRunning: false, originalUrl: '', finalUrl: '' };
    }

    // 用户操作日志记录函数
    function logUserAction(message) {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] ${message}`);

        const logPanel = document.querySelector('#userLogPanel');
        if (logPanel) {
            const logEntry = document.createElement('div');
            logEntry.style.cssText = `
                padding: 5px 0;
                border-bottom: 1px solid #eee;
                font-size: 12px;
            `;
            logEntry.innerHTML = `<span style="color:#999;">[${timestamp}]</span> ${message}`;
            logPanel.insertBefore(logEntry, logPanel.firstChild);

            // 限制日志条目数量
            if (logPanel.children.length > 50) {
                logPanel.removeChild(logPanel.lastChild);
            }
        }

        // 同时更新页面切换日志面板
        const pageSwitchLogPanel = document.querySelector('#pageSwitchLogPanel');
        if (pageSwitchLogPanel && message.includes('切换')) {
            const logEntry = document.createElement('div');
            logEntry.style.cssText = `
                padding: 5px 0;
                border-bottom: 1px solid #eee;
                font-size: 12px;
            `;
            logEntry.innerHTML = `<span style="color:#999;">[${timestamp}]</span> ${message}`;
            pageSwitchLogPanel.insertBefore(logEntry, pageSwitchLogPanel.firstChild);

            // 限制日志条目数量
            if (pageSwitchLogPanel.children.length > 50) {
                pageSwitchLogPanel.removeChild(pageSwitchLogPanel.lastChild);
            }
        }
    }

    // 显示通知
    function showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? '#4CAF50' : type === 'info' ? '#2196F3' : '#F44336'};
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            z-index: 10000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        `;
        document.body.appendChild(toast);
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transition = 'opacity 0.5s';
            setTimeout(() => document.body.removeChild(toast), 500);
        }, 3000);
    }

    // 更新当前状态显示
    function updateCurrentStatus(message, type = 'info') {
        const statusPanel = document.querySelector('#currentStatus');
        if (statusPanel) {
            statusPanel.textContent = message;

            // 根据类型设置不同的样式
            let backgroundColor, color;
            switch (type) {
                case 'processing':
                    backgroundColor = '#fff3cd';
                    color = '#856404';
                    break;
                case 'success':
                    backgroundColor = '#d4edda';
                    color = '#155724';
                    break;
                case 'error':
                    backgroundColor = '#f8d7da';
                    color = '#721c24';
                    break;
                case 'waiting':
                    backgroundColor = '#d1ecf1';
                    color = '#0c5460';
                    break;
                default:
                    backgroundColor = '#f8f9fa';
                    color = '#666';
            }

            statusPanel.style.backgroundColor = backgroundColor;
            statusPanel.style.color = color;
        }
    }

    /**
     * 等待元素出现的辅助函数
     *
     * @param {string} selector - CSS选择器
     * @param {number} timeout - 超时时间（毫秒）
     * @returns {Promise<Element>} - 返回找到的元素
     */
    function waitForElement(selector, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            function checkElement() {
                const element = document.querySelector(selector);
                if (element && element.offsetParent !== null) {
                    resolve(element);
                    return;
                }

                if (Date.now() - startTime > timeout) {
                    reject(`等待元素超时: ${selector}`);
                    return;
                }

                setTimeout(checkElement, 50);
            }

            checkElement();
        });
    }

    /**
     * 等待元素列表出现的辅助函数
     *
     * @param {string} selector - CSS选择器
     * @param {number} minCount - 最小需要的元素数量
     * @param {number} timeout - 超时时间（毫秒）
     * @returns {Promise<NodeList>} - 返回找到的元素列表
     */
    function waitForElements(selector, minCount = 1, timeout = 5000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            function checkElements() {
                const elements = document.querySelectorAll(selector);
                if (elements.length >= minCount) {
                    resolve(elements);
                    return;
                }

                if (Date.now() - startTime > timeout) {
                    reject(`等待元素列表超时: ${selector} (需要至少 ${minCount} 个)`);
                    return;
                }

                setTimeout(checkElements, 50);
            }

            checkElements();
        });
    }

    // =============== 自动点赞功能 ===============

    // 獲取待點讚的貼文列表
    function getPendingPosts() {
        // 每次都从localStorage获取最新数据
        const currentTargetPosts = JSON.parse(localStorage.getItem('fb_auto_like_posts_global') || '[]');
        const currentLikedPosts = getCurrentAccountLikedPosts();

        // 日志输出当前状态，帮助调试
        console.log('当前全局待处理链接:', currentTargetPosts);
        console.log('当前账号已点赞链接:', currentLikedPosts);

        // 过滤出未点赞的链接
        const pendingPosts = currentTargetPosts.filter(url => !currentLikedPosts.includes(url));
        console.log('过滤后待处理链接:', pendingPosts);

        return pendingPosts;
    }

    // 更新顯示待點讚列表
    function updatePendingList() {
        const pendingList = document.querySelector('#pendingList');
        if (!pendingList) return;

        const pendingPosts = getPendingPosts();
        pendingList.innerHTML = '';
        pendingPosts.forEach(url => {
            const item = document.createElement('div');
            item.style.cssText = `
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 5px;
                border-bottom: 1px solid #eee;
            `;

            const urlText = document.createElement('div');
            urlText.style.cssText = `
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                margin-right: 10px;
            `;
            urlText.textContent = url;

            const deleteBtn = document.createElement('button');
            deleteBtn.innerHTML = '×';
            deleteBtn.style.cssText = `
                background: none;
                border: none;
                color: #dc3545;
                cursor: pointer;
                font-size: 18px;
                padding: 0 5px;
            `;
            deleteBtn.onclick = (e) => {
                e.stopPropagation();
                const index = targetPosts.indexOf(url);
                if (index > -1) {
                    targetPosts.splice(index, 1);
                    localStorage.setItem('fb_auto_like_posts_global', JSON.stringify(targetPosts));
                    updatePendingList();
                    logUserAction(`删除链接: ${url}`);
                }
            };

            item.appendChild(urlText);
            item.appendChild(deleteBtn);
            pendingList.appendChild(item);
        });
    }

    // 解析输入文本中的 Facebook 链接
    function parseFacebookUrls(text) {
        // Facebook URL 的正则表达式模式
        const fbPatterns = [
            // 标准分享链接格式 (确保ID至少有5个字符)
            /https:\/\/www\.facebook\.com\/share\/[a-zA-Z0-9]{5,}(?:\/)?/g,
            // 带 p 的分享链接格式 (确保ID至少有5个字符)
            /https:\/\/www\.facebook\.com\/share\/p\/[a-zA-Z0-9]{5,}(?:\/)?/g,
            // 带 v 的分享链接格式 (确保ID至少有5个字符)
            /https:\/\/www\.facebook\.com\/share\/v\/[a-zA-Z0-9]{5,}(?:\/)?/g,
            // 其他可能的分享链接格式
            /https:\/\/www\.facebook\.com\/[a-zA-Z0-9.]+\/posts\/[a-zA-Z0-9]{5,}(?:\/)?/g,
            /https:\/\/www\.facebook\.com\/permalink\.php\?story_fbid=[a-zA-Z0-9]{5,}(?:&[^&]*)*$/g
        ];

        // 存储所有匹配的链接
        let allUrls = [];

        // 使用每个模式匹配链接
        fbPatterns.forEach(pattern => {
            const matches = text.match(pattern) || [];
            allUrls = allUrls.concat(matches);
        });

        // 规范化链接格式并验证
        const normalizedUrls = allUrls
            .map(url => {
                // 移除链接末尾的查询参数
                url = url.split('?')[0];
                // 确保链接末尾有 /
                return url.endsWith('/') ? url : `${url}/`;
            })
            .filter(url => {
                // 验证链接是否包含有效的ID
                const hasValidId = url.match(/[a-zA-Z0-9]{5,}/);
                // 验证链接不是仅包含基本路径
                const isNotBasePath = !url.match(/\/share\/$/) && !url.match(/\/share\/p\/$/);
                return hasValidId && isNotBasePath;
            });

        // 去重
        const uniqueUrls = [...new Set(normalizedUrls)];

        console.log(`解析到 ${uniqueUrls.length} 个有效的分享链接`);
        return uniqueUrls;
    }

    // 添加到点赞列表
    function addToLikeList(input) {
        if (!input) return false;

        const urls = parseFacebookUrls(input);
        if (urls.length === 0) {
            alert('未找到有效的 Facebook 链接！');
            return false;
        }

        // 获取最新的全局待点赞列表和当前账号的已点赞列表
        const currentTargetPosts = JSON.parse(localStorage.getItem('fb_auto_like_posts_global') || '[]');
        const currentLikedPosts = getCurrentAccountLikedPosts();

        let addedCount = 0;
        urls.forEach(url => {
            // 检查链接是否已在待点赞列表或当前账号的已点赞列表中
            if (!currentTargetPosts.includes(url) && !currentLikedPosts.includes(url)) {
                currentTargetPosts.push(url);
                addedCount++;
            }
        });

        if (addedCount > 0) {
            // 更新全局待点赞列表
            localStorage.setItem('fb_auto_like_posts_global', JSON.stringify(currentTargetPosts));

            // 更新内存中的数组
            targetPosts.length = 0;
            currentTargetPosts.forEach(url => targetPosts.push(url));

            logUserAction(`批量添加 ${addedCount} 个链接到全局待点赞列表`);
            updatePendingList();
            return true;
        }

        alert('所有链接都已经在列表中！');
        return false;
    }

    // 标记已点赞
    function markAsLiked(url) {
        // 添加到当前账号的已点赞记录
        if (!likedPosts.includes(url)) {
            likedPosts.push(url);
            saveCurrentAccountLikedPosts(likedPosts);
            logUserAction(`已添加到当前账号点赞记录: ${url}`);
        }

        // 更新UI显示
        updatePendingList();
        updateAccountInfo();
    }

    // 检查是否已经点赞成功 - 超强版
    function checkLikeSuccess() {
        try {
            // 方法1: 查找"移除赞"按钮 - 这是最可靠的点赞成功指示
            const removeLikeButton = document.querySelector('[aria-label="移除赞"]');

            // 方法2: 检查是否有蓝色高亮的点赞按钮
            const highlightedLikeButton = document.querySelector('[aria-label="赞"][role="button"][class*="active"]');

            // 方法3: 检查是否有蓝色填充的点赞图标
            const likeIcon = document.querySelector('svg[aria-label="赞"][fill="#1877F2"]');

            // 方法4: 检查点赞按钮是否有蓝色背景
            const likeButtons = document.querySelectorAll('[aria-label="赞"][role="button"]');
            let hasBlueBackground = false;
            let hasBlueIcon = false;

            likeButtons.forEach(button => {
                try {
                    const computedStyle = window.getComputedStyle(button);
                    const backgroundColor = computedStyle.backgroundColor;
                    const color = computedStyle.color;

                    // 检查是否有蓝色背景 (Facebook的蓝色)
                    if (backgroundColor.includes('rgb(24, 119, 242)') ||
                        backgroundColor.includes('#1877f2') ||
                        backgroundColor.includes('rgb(66, 103, 178)')) {
                        hasBlueBackground = true;
                    }

                    // 检查文字颜色是否为蓝色
                    if (color.includes('rgb(24, 119, 242)') ||
                        color.includes('#1877f2') ||
                        color.includes('rgb(66, 103, 178)')) {
                        hasBlueIcon = true;
                    }

                    // 检查按钮内部的SVG图标
                    const svgIcon = button.querySelector('svg');
                    if (svgIcon) {
                        const fill = svgIcon.getAttribute('fill');
                        const style = svgIcon.getAttribute('style');
                        const paths = svgIcon.querySelectorAll('path');

                        if (fill === '#1877F2' || (style && style.includes('#1877F2'))) {
                            hasBlueIcon = true;
                        }

                        // 检查path元素的fill属性
                        paths.forEach(path => {
                            const pathFill = path.getAttribute('fill');
                            const pathStyle = path.getAttribute('style');
                            if (pathFill === '#1877F2' || (pathStyle && pathStyle.includes('#1877F2'))) {
                                hasBlueIcon = true;
                            }
                        });
                    }
                } catch (e) {
                    // 忽略样式检查错误
                }
            });

            // 方法5: 检查是否有包含"已赞"或相关文本的元素
            const likedText = document.querySelector('[aria-label*="已赞"]') ||
                             document.querySelector('[title*="已赞"]') ||
                             document.querySelector('[aria-label*="取消赞"]') ||
                             document.querySelector('[aria-label*="Unlike"]');

            // 方法6: 检查是否有特定的CSS类名表示已点赞状态
            const likedByClass = document.querySelector('.liked') ||
                                document.querySelector('[class*="liked"]') ||
                                document.querySelector('[data-testid*="unlike"]');

            // 方法7: 检查点赞数字是否发生变化（如果页面有显示点赞数）
            const likeCountElements = document.querySelectorAll('[aria-label*="个赞"]');
            let hasLikeCount = likeCountElements.length > 0;

            const result = !!removeLikeButton || !!highlightedLikeButton || !!likeIcon ||
                          hasBlueBackground || hasBlueIcon || !!likedText || !!likedByClass;

            // 详细日志记录
            const details = {
                removeLikeButton: !!removeLikeButton,
                highlightedLikeButton: !!highlightedLikeButton,
                likeIcon: !!likeIcon,
                hasBlueBackground: hasBlueBackground,
                hasBlueIcon: hasBlueIcon,
                likedText: !!likedText,
                likedByClass: !!likedByClass,
                hasLikeCount: hasLikeCount,
                totalLikeButtons: likeButtons.length
            };

            console.log('点赞状态检查:', result ? '✅ 已点赞' : '❌ 未点赞', details);

            // 如果检测到已点赞，额外记录详细信息
            if (result) {
                logUserAction(`点赞状态确认: ${JSON.stringify(details)}`);
            }

            return result;
        } catch (error) {
            console.error('点赞状态检查出错:', error);
            logUserAction(`点赞状态检查出错: ${error.message}`);
            return false;
        }
    }

    // 查找點讚按鈕
    function findLikeButton() {
        const likeButtons = Array.from(document.querySelectorAll('[aria-label="赞"][role="button"]'));
        console.log('找到点赞按钮数量:', likeButtons.length);

        // 排除 MainFeed 元素内的按钮 (适用于所有格式)
        const mainFeedElement = document.querySelector('[data-pagelet="MainFeed"]');

        if (mainFeedElement && likeButtons.length > 0) {
            // 过滤掉在 MainFeed 元素内的按钮
            const filteredButtons = likeButtons.filter(button => {
                return !mainFeedElement.contains(button);
            });

            console.log('排除 MainFeed 内的按钮后剩余数量:', filteredButtons.length);

            // 使用第一个不在 MainFeed 内的按钮
            if (filteredButtons.length > 0) {
                console.log('使用不在 MainFeed 内的第一个点赞按钮');
                return filteredButtons[0];
            }
        }

        // 如果没有找到符合条件的按钮或没有 MainFeed 元素，使用默认逻辑
        if (likeButtons.length >= 2) {
            console.log('使用第二个点赞按钮');
            return likeButtons[1];
        } else if (likeButtons.length === 1) {
            console.log('只找到一个点赞按钮');
            return likeButtons[0];
        }

        console.log('未找到点赞按钮');
        return null;
    }

    // 執行點讚
    async function performLike() {
        // 获取最新的待处理链接
        const pendingPosts = getPendingPosts();
        console.log('准备处理的链接列表:', pendingPosts);
        
        if (pendingPosts.length === 0) {
            // 添加醒目的完成日志
            const completionMessage = '✅✅✅ 所有链接已全部点赞完成 ✅✅✅';
            logUserAction(completionMessage);
            updateCurrentStatus('🎉 所有链接已全部点赞完成！', 'success');
        
            // 在日志面板添加醒目的完成标记
            const logPanel = document.querySelector('#userLogPanel');
            if (logPanel) {
                const completionNotice = document.createElement('div');
                completionNotice.style.cssText = `
                    padding: 10px;
                    margin: 10px 0;
                    background-color: #d4edda;
                    color: #155724;
                    border: 1px solid #c3e6cb;
                    border-radius: 4px;
                    font-weight: bold;
                    text-align: center;
                `;
                completionNotice.textContent = '🎉 全部点赞任务已完成 🎉';
                logPanel.insertBefore(completionNotice, logPanel.firstChild);
            }
        
            // 显示多重提示
            showToast('🎉 所有贴文已完成点赞！', 'success');
            setTimeout(() => {
                alert('所有贴文已点赞完成！');
            }, 500);
        
            // 确保停止自动点赞流程
            isRunning = false;
            currentOriginalUrl = '';
            currentFinalUrl = '';
            saveCurrentState(false, '', '');
            
            // 更新UI状态
            const startBtn = document.getElementById('startAutoLikeBtn');
            const stopBtn = document.getElementById('stopAutoLikeBtn');
            
            if (startBtn && stopBtn) {
                startBtn.disabled = false;
                startBtn.style.opacity = '1';
                stopBtn.disabled = true;
                stopBtn.style.opacity = '0.6';
            }
            
            return;
        }

        const nextPost = pendingPosts[0];
        logUserAction(`正在处理链接: ${nextPost}`);
        updateCurrentStatus(`🔄 正在处理: ${nextPost.substring(0, 50)}...`, 'processing');

        currentOriginalUrl = nextPost;
        currentFinalUrl = '';  // 重置最终URL
        saveCurrentState(true, nextPost, '');

        // 智能页面跳转逻辑 - 避免无限刷新
        const currentUrl = window.location.href;
        const needsRedirect = !currentUrl.includes(nextPost.split('/').pop()) && currentUrl !== nextPost;

        if (needsRedirect) {
            // 检查是否最近刚刚跳转过，避免频繁跳转
            const lastRedirectTime = parseInt(localStorage.getItem('fb_last_redirect_time') || '0');
            const currentTime = Date.now();

            if (currentTime - lastRedirectTime < 5000) {
                logUserAction('检测到频繁跳转，延迟处理以避免无限循环');
                updateCurrentStatus('⏳ 延迟跳转以避免频繁刷新...', 'waiting');
                setTimeout(() => {
                    if (isRunning) {
                        performLike();
                    }
                }, 10000);
                return;
            }

            // 记录跳转时间
            localStorage.setItem('fb_last_redirect_time', currentTime.toString());
            updateCurrentStatus(`🔄 正在跳转到目标页面...`, 'processing');
            logUserAction(`从 ${currentUrl} 跳转到 ${nextPost}`);

            // 使用更安全的跳转方式
            try {
                window.location.href = nextPost;
            } catch (error) {
                logUserAction(`跳转失败: ${error.message}`);
                // 跳转失败时跳过当前链接
                currentOriginalUrl = '';
                currentFinalUrl = '';
                saveCurrentState(true, '', '');
                setTimeout(() => {
                    if (isRunning) {
                        performLike();
                    }
                }, 3000);
            }
            return;
        }

        // 如果已在目标页面，直接检查点赞状态
        checkAndLike(nextPost);
    }

    // 新增清除待处理链接的函数
    function clearAllPendingLinks() {
        // 清除内存中的数组
        targetPosts.length = 0;

        // 清除本地存储中的全局待处理链接
        localStorage.removeItem('fb_auto_like_posts_global');

        // 更新显示
        const pendingList = document.querySelector('#pendingList');
        if (pendingList) {
            pendingList.innerHTML = '';
        }

        logUserAction('清除所有待处理链接（全局共享）');
        updateAccountInfo();
    }

    // 等待页面和内容完全加载
    function waitForPageAndContentReady() {
        return new Promise((resolve) => {
            let attempts = 0;
            const maxAttempts = 20; // 最多等待20秒

            const checkReady = () => {
                attempts++;

                // 检查基本页面状态
                const isDocumentReady = document.readyState === 'complete';

                // 检查是否有点赞按钮或已点赞的指示
                const hasLikeButton = document.querySelector('[aria-label="赞"][role="button"]');
                const hasLikedIndicator = document.querySelector('[aria-label="移除赞"]');

                // 检查页面是否有基本的Facebook内容结构
                const hasBasicStructure = document.querySelector('[role="main"]') ||
                                        document.querySelector('[data-pagelet]') ||
                                        document.querySelector('#facebook');

                if (isDocumentReady && hasBasicStructure && (hasLikeButton || hasLikedIndicator)) {
                    logUserAction('页面和内容已完全加载');
                    resolve();
                    return;
                }

                if (attempts >= maxAttempts) {
                    logUserAction('页面加载超时，但继续尝试处理');
                    resolve();
                    return;
                }

                setTimeout(checkReady, 1000);
            };

            checkReady();
        });
    }

    // 检查并点赞 - 增强版
    async function checkAndLike(originalUrl) {
        try {
            updateCurrentStatus(`⏳ 等待页面完全加载...`, 'processing');

            // 等待页面和内容完全加载
            await waitForPageAndContentReady();

            // 记录当前页面URL
            if (!currentFinalUrl) {
                currentFinalUrl = window.location.href;
                saveCurrentState(true, originalUrl, currentFinalUrl);
                logUserAction(`记录最终URL: ${currentFinalUrl}`);
            }

            updateCurrentStatus(`🔍 检查点赞状态...`, 'processing');

            // 多次检查点赞状态，确保准确性
            let likeCheckCount = 0;
            let isAlreadyLiked = false;

            while (likeCheckCount < 3) {
                likeCheckCount++;
                isAlreadyLiked = checkLikeSuccess();

                if (isAlreadyLiked) {
                    logUserAction(`第${likeCheckCount}次检查：检测到已点赞`);
                    break;
                } else {
                    logUserAction(`第${likeCheckCount}次检查：未检测到点赞`);
                    if (likeCheckCount < 3) {
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                }
            }

            if (isAlreadyLiked) {
                logUserAction(`确认已点赞: ${originalUrl}`);
                updateCurrentStatus(`✅ 已点赞，准备下一个链接`, 'success');

                // 标记为已点赞并从待处理列表中移除
                markAsLiked(originalUrl);

                // 清除当前处理状态
                currentOriginalUrl = '';
                currentFinalUrl = '';
                saveCurrentState(true, '', '');

                // 显示点赞成功的提示
                showToast('✅ 点赞成功！', 'success');

                // 延迟处理下一个链接
                setTimeout(() => {
                    if (isRunning) {
                        performLike();
                    }
                }, 3000); // 增加延迟时间
                return;
            }

            // 如果未点赞，开始点赞流程
            updateCurrentStatus(`👆 开始点赞流程...`, 'processing');
            await performLikeAction(originalUrl);

        } catch (error) {
            logUserAction(`检查点赞过程出错: ${error.message}`);
            updateCurrentStatus(`❌ 处理出错，跳过当前链接`, 'error');

            // 出错时跳过当前链接
            currentOriginalUrl = '';
            currentFinalUrl = '';
            saveCurrentState(true, '', '');
            setTimeout(() => {
                if (isRunning) {
                    performLike();
                }
            }, 3000);
        }
    }

    // 执行点赞操作 - 新的稳定版本
    async function performLikeAction(originalUrl) {
        return new Promise((resolve) => {
            let retryCount = 0;
            const maxRetries = 3;
            let isProcessing = false; // 防止重复处理

            const tryLike = setInterval(async () => {
                if (!isRunning || isProcessing) {
                    clearInterval(tryLike);
                    resolve();
                    return;
                }

                retryCount++;
                logUserAction(`尝试点赞 (第 ${retryCount} 次)`);
                updateCurrentStatus(`👆 尝试点赞 (${retryCount}/${maxRetries})`, 'processing');

                // 再次检查是否已点赞成功
                if (checkLikeSuccess()) {
                    isProcessing = true;
                    clearInterval(tryLike);
                    logUserAction(`✅ 点赞成功确认: ${originalUrl}`);
                    updateCurrentStatus(`✅ 点赞成功，准备下一个链接`, 'success');
                    markAsLiked(originalUrl);
                    currentOriginalUrl = '';
                    currentFinalUrl = '';
                    saveCurrentState(true, '', '');
                    showToast('✅ 点赞成功！', 'success');
                    setTimeout(() => {
                        if (isRunning) {
                            performLike();
                        }
                    }, 3000);
                    resolve();
                    return;
                }

                const likeButton = findLikeButton();
                if (likeButton) {
                    try {
                        // 确保按钮可见
                        likeButton.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        await new Promise(resolve => setTimeout(resolve, 500));

                        likeButton.click();
                        logUserAction(`点击点赞按钮 (第 ${retryCount} 次)`);

                        // 等待点赞确认
                        setTimeout(() => {
                            if (checkLikeSuccess() && !isProcessing) {
                                isProcessing = true;
                                clearInterval(tryLike);
                                logUserAction(`✅ 点赞成功: ${originalUrl}`);
                                updateCurrentStatus(`✅ 点赞成功，准备下一个链接`, 'success');
                                markAsLiked(originalUrl);
                                currentOriginalUrl = '';
                                currentFinalUrl = '';
                                saveCurrentState(true, '', '');
                                showToast('✅ 点赞成功！', 'success');
                                setTimeout(() => {
                                    if (isRunning) {
                                        performLike();
                                    }
                                }, 3000);
                                resolve();
                            }
                        }, 2000);
                    } catch (error) {
                        logUserAction(`点赞操作出错: ${error.message}`);
                    }
                } else {
                    logUserAction(`未找到点赞按钮 (第 ${retryCount} 次)`);
                    updateCurrentStatus(`⚠️ 未找到点赞按钮 (${retryCount}/${maxRetries})`, 'error');
                }

                // 如果达到最大重试次数，放弃当前链接
                if (retryCount >= maxRetries) {
                    isProcessing = true;
                    clearInterval(tryLike);
                    logUserAction(`⚠️ 达到最大重试次数，跳过: ${originalUrl}`);
                    updateCurrentStatus(`⚠️ 重试次数用完，跳过当前链接`, 'error');
                    currentOriginalUrl = '';
                    currentFinalUrl = '';
                    saveCurrentState(true, '', '');
                    setTimeout(() => {
                        if (isRunning) {
                            performLike();
                        }
                    }, 3000);
                    resolve();
                }
            }, 4000); // 增加间隔到4秒

            // 设置总体超时
            setTimeout(() => {
                if (!isProcessing) {
                    isProcessing = true;
                    clearInterval(tryLike);
                    logUserAction(`⏰ 处理超时，跳过: ${originalUrl}`);
                    updateCurrentStatus(`⏰ 处理超时，跳过当前链接`, 'error');
                    currentOriginalUrl = '';
                    currentFinalUrl = '';
                    saveCurrentState(true, '', '');
                    setTimeout(() => {
                        if (isRunning) {
                            performLike();
                        }
                    }, 3000);
                    resolve();
                }
            }, 25000); // 增加超时时间到25秒
        });
    }

    // 開始自動點讚流程
    function startAutoLike() {
        if (isRunning) {
            logUserAction('自动点赞已在运行中');
            return;
        }

        const pendingPosts = getPendingPosts();
        if (pendingPosts.length === 0) {
            logUserAction('没有待处理的链接');
            alert('没有待点赞的链接！');
            return;
        }

        isRunning = true;
        saveCurrentState(true, '', '');
        logUserAction(`开始自动点赞 - 待处理数量: ${pendingPosts.length}`);
        updateCurrentStatus(`🚀 开始自动点赞 - 待处理: ${pendingPosts.length} 个链接`, 'processing');
        
        // 更新UI状态
        const startBtn = document.getElementById('startAutoLikeBtn');
        const stopBtn = document.getElementById('stopAutoLikeBtn');
        const skipBtn = document.getElementById('skipCurrentBtn');
        const forceMarkBtn = document.getElementById('forceMarkBtn');

        if (startBtn && stopBtn) {
            startBtn.disabled = true;
            startBtn.style.opacity = '0.6';
            stopBtn.disabled = false;
            stopBtn.style.opacity = '1';
        }

        if (skipBtn) {
            skipBtn.disabled = false;
            skipBtn.style.opacity = '1';
        }

        if (forceMarkBtn) {
            forceMarkBtn.disabled = false;
            forceMarkBtn.style.opacity = '1';
        }
        
        performLike();
    }

    // 停止自動點讚
    function stopAutoLike() {
        isRunning = false;
        currentOriginalUrl = '';
        currentFinalUrl = '';
        saveCurrentState(false, '', '');
        logUserAction('停止自动点赞');
        updateCurrentStatus('⏹️ 已停止自动点赞', 'info');

        // 更新UI状态
        const startBtn = document.getElementById('startAutoLikeBtn');
        const stopBtn = document.getElementById('stopAutoLikeBtn');
        const skipBtn = document.getElementById('skipCurrentBtn');
        const forceMarkBtn = document.getElementById('forceMarkBtn');

        if (startBtn && stopBtn) {
            startBtn.disabled = false;
            startBtn.style.opacity = '1';
            stopBtn.disabled = true;
            stopBtn.style.opacity = '0.6';
        }

        if (skipBtn) {
            skipBtn.disabled = true;
            skipBtn.style.opacity = '0.6';
        }

        if (forceMarkBtn) {
            forceMarkBtn.disabled = true;
            forceMarkBtn.style.opacity = '0.6';
        }
    }

    // 跳过当前链接
    function skipCurrentLink() {
        if (!isRunning || !currentOriginalUrl) {
            logUserAction('没有正在处理的链接');
            updateCurrentStatus('⚠️ 没有正在处理的链接', 'error');
            return;
        }

        logUserAction(`⏭️ 手动跳过当前链接: ${currentOriginalUrl}`);
        updateCurrentStatus('⏭️ 跳过当前链接，准备下一个', 'waiting');

        // 清除当前状态
        currentOriginalUrl = '';
        currentFinalUrl = '';
        saveCurrentState(true, '', '');

        // 继续处理下一个链接
        setTimeout(() => {
            if (isRunning) {
                performLike();
            }
        }, 1000);
    }

    // 强制标记当前链接为已点赞
    function forceMarkAsLiked() {
        if (!currentOriginalUrl) {
            logUserAction('没有正在处理的链接');
            updateCurrentStatus('⚠️ 没有正在处理的链接', 'error');
            showToast('没有正在处理的链接', 'error');
            return;
        }

        logUserAction(`🔧 强制标记为已点赞: ${currentOriginalUrl}`);
        updateCurrentStatus('✅ 强制标记为已点赞，准备下一个', 'success');

        // 标记为已点赞
        markAsLiked(currentOriginalUrl);

        // 清除当前状态
        currentOriginalUrl = '';
        currentFinalUrl = '';
        saveCurrentState(true, '', '');

        showToast('✅ 已强制标记为点赞！', 'success');

        // 继续处理下一个链接
        setTimeout(() => {
            if (isRunning) {
                performLike();
            }
        }, 2000);
    }

    // 清除点赞记录
    function clearLikeHistory() {
        // 清除当前账号的点赞记录
        likedPosts.length = 0;
        saveCurrentAccountLikedPosts(likedPosts);
        logUserAction('清除当前账号的点赞记录');
        updateAccountInfo();
    }

    // 清除所有链接
    function clearAllLinks() {
        // 清除全局待点赞列表
        localStorage.removeItem('fb_auto_like_posts_global');
        targetPosts.length = 0;

        // 清除当前账号的已点赞记录
        likedPosts.length = 0;
        saveCurrentAccountLikedPosts(likedPosts);

        // 清除状态
        localStorage.removeItem('fb_auto_like_state');

        logUserAction('清除所有链接和记录');
    }

    // 清除日志记录
    function clearLogs() {
        const logPanel = document.querySelector('#userLogPanel');
        if (logPanel) {
            logPanel.innerHTML = '';
        }

        const pageSwitchLogPanel = document.querySelector('#pageSwitchLogPanel');
        if (pageSwitchLogPanel) {
            pageSwitchLogPanel.innerHTML = '';
        }
    }

    // =============== 页面切换功能 ===============

    /**
     * 超级快速切换（一键完成，无需多次点击）- 优化版
     * 这是一键切换功能的主入口函数
     */
    function performSuperFastSwitch() {
        if (savedPageNames.length === 0) {
            logUserAction('没有保存的公共主页名称，自动获取公共主页');
            showToast('正在自动获取公共主页...', 'info');

            // 先获取公共主页，然后自动执行切换
            getAllPublicPagesAndThenSwitch();
            return;
        }

        const targetIndex = savedPageNames.length - 1 - currentPageIndex;
        const targetPageName = savedPageNames[targetIndex];

        logUserAction(`⚡ 超级快速切换到: ${targetPageName}`);
        showToast(`正在切换到: ${targetPageName}`, 'info');

        // 使用最优化的切换策略
        executeSuperFastSwitch(targetPageName)
            .then(() => {
                logUserAction(`🎉 超级快速切换成功: ${targetPageName}`);
                showToast(`✅ 已切换到: ${targetPageName}`, 'success');

                // 切换成功后刷新当前账号数据
                setTimeout(() => {
                    refreshCurrentAccountData();
                    logUserAction(`🔄 已刷新账号数据，当前账号: ${getCurrentAccountName()}`);
                }, 1000);
            })
            .catch((error) => {
                logUserAction(`❌ 超级快速切换失败: ${error}`);
                showToast(`❌ 切换失败: ${error}`, 'error');

                // 失败后自动重试一次
                setTimeout(() => {
                    logUserAction(`🔄 自动重试切换到: ${targetPageName}`);
                    executeSuperFastSwitch(targetPageName, true)
                        .then(() => {
                            logUserAction(`🎉 重试切换成功: ${targetPageName}`);
                            showToast(`✅ 已切换到: ${targetPageName}`, 'success');

                            // 重试成功后也刷新当前账号数据
                            setTimeout(() => {
                                refreshCurrentAccountData();
                                logUserAction(`🔄 已刷新账号数据，当前账号: ${getCurrentAccountName()}`);
                            }, 1000);
                        })
                        .catch((retryError) => {
                            logUserAction(`❌ 重试切换失败: ${retryError}`);
                            showToast(`❌ 切换失败，请手动切换`, 'error');
                        });
                }, 500);
            });
    }

    // 获取所有公共主页然后执行超级快速切换
    function getAllPublicPagesAndThenSwitch() {
        logUserAction('开始获取所有公共主页并执行超级快速切换');

        const observer = new MutationObserver((mutations, obs) => {
            const profileButton = document.querySelector('[aria-label="你的个人主页"][role="button"]');
            if (profileButton) {
                obs.disconnect();
                executeGetPagesSequence(profileButton);
            }
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        const profileButton = document.querySelector('[aria-label="你的个人主页"][role="button"]');
        if (profileButton) {
            observer.disconnect();
            executeGetPagesSequence(profileButton);
        }

        setTimeout(() => {
            observer.disconnect();
            logUserAction('获取公共主页超时，请手动尝试');
            showToast('获取公共主页超时，请刷新页面重试', 'error');
        }, 10000);
    }

    // 执行获取页面的完整序列
    function executeGetPagesSequence(profileButton) {
        profileButton.click();
        logUserAction('已点击个人主页按钮');

        waitForElement('[aria-label="查看所有主页"][role="button"]', 2000)
            .then(viewAllPagesButton => {
                viewAllPagesButton.click();
                logUserAction('已点击"查看所有主页"按钮');

                return waitForElements('[role="list"] > [data-visualcompletion="ignore-dynamic"][role="listitem"][style*="padding-left: 8px; padding-right: 8px;"] > [aria-label]', 1, 3000);
            })
            .then(pageItems => {
                publicPages = [];
                const currentPageNames = [];

                pageItems.forEach((item, index) => {
                    const pageName = item.getAttribute('aria-label');
                    if (pageName) {
                        publicPages.push({
                            name: pageName,
                            element: item,
                            index: index
                        });
                        currentPageNames.push(pageName);
                    }
                });

                if (currentPageNames.length > 0) {
                    savedPageNames = currentPageNames;
                    localStorage.setItem('fb_saved_page_names', JSON.stringify(savedPageNames));
                    logUserAction(`已保存 ${savedPageNames.length} 个公共主页名称到本地存储`);

                    document.body.click();

                    currentPageIndex = 0;
                    localStorage.setItem('fb_current_page_index', '0');

                    setTimeout(() => {
                        performSuperFastSwitch();
                    }, 300);
                } else {
                    logUserAction('未找到任何公共主页');
                    showToast('未找到任何公共主页', 'error');
                    document.body.click();
                }
            })
            .catch(error => {
                logUserAction(`获取公共主页过程中出错: ${error}`);
                showToast('获取公共主页失败，请重试', 'error');
                document.body.click();
            });
    }

    /**
     * 执行超级快速切换的核心函数
     *
     * @param {string} targetPageName - 目标公共主页名称
     * @param {boolean} isRetry - 是否为重试操作
     * @returns {Promise} - 返回切换操作的Promise
     */
    function executeSuperFastSwitch(targetPageName, isRetry = false) {
        return new Promise(async (resolve, reject) => {
            try {
                logUserAction(`🚀 开始${isRetry ? '重试' : ''}超级快速切换流程`);

                const selectors = {
                    profileButton: '[aria-label="你的个人主页"][role="button"]',
                    viewAllPages: '[aria-label="查看所有主页"][role="button"]',
                    pageItems: '[role="list"] > [data-visualcompletion="ignore-dynamic"][role="listitem"][style*="padding-left: 8px; padding-right: 8px;"] > [aria-label]'
                };

                const profileButton = document.querySelector(selectors.profileButton);
                if (!profileButton) {
                    throw new Error('未找到个人主页按钮');
                }

                profileButton.click();
                logUserAction('已点击个人主页按钮');

                await new Promise(resolve => setTimeout(resolve, 200));

                let viewAllPagesButton;
                try {

                    viewAllPagesButton = await waitForElement(selectors.viewAllPages, 1500);
                    viewAllPagesButton.click();
                    logUserAction('已点击"查看所有主页"按钮');
                } catch (error) {
                    logUserAction('未找到"查看所有主页"按钮，尝试重新点击个人主页按钮');
                    document.body.click();
                    await new Promise(resolve => setTimeout(resolve, 200));
                    profileButton.click();

                    try {
                        viewAllPagesButton = await waitForElement(selectors.viewAllPages, 1500);
                        viewAllPagesButton.click();
                        logUserAction('第二次尝试成功点击"查看所有主页"按钮');
                    } catch (retryError) {
                        throw new Error('无法找到"查看所有主页"按钮，请检查Facebook界面是否有变化');
                    }
                }

                let pageItems;
                try {
                    pageItems = await waitForElements(selectors.pageItems, 1, 2000);
                } catch (error) {
                    throw new Error('加载公共主页列表超时，Facebook可能加载缓慢');
                }

                const foundPageNames = Array.from(pageItems).map(item => item.getAttribute('aria-label'));
                logUserAction(`找到 ${pageItems.length} 个公共主页: ${foundPageNames.join(', ')}`);

                let targetFound = false;

                for (const item of pageItems) {
                    const pageName = item.getAttribute('aria-label');
                    if (pageName === targetPageName) {
                        try {
                            item.click();
                            logUserAction(`已点击目标公共主页: ${targetPageName}`);
                            targetFound = true;
                        } catch (e) {
                            logUserAction(`直接点击失败，尝试替代方法: ${e.message}`);

                            try {
                                // 尝试方法1：使用MouseEvent
                                const clickEvent = new MouseEvent('click', {
                                    bubbles: true,
                                    cancelable: true,
                                    view: window
                                });
                                item.dispatchEvent(clickEvent);
                                targetFound = true;
                                logUserAction(`使用MouseEvent点击成功`);
                            } catch (e2) {
                                try {
                                    // 尝试方法2：模拟鼠标点击序列
                                    const rect = item.getBoundingClientRect();
                                    const centerX = rect.left + rect.width / 2;
                                    const centerY = rect.top + rect.height / 2;

                                    ['mousedown', 'mouseup', 'click'].forEach(eventType => {
                                        const event = new MouseEvent(eventType, {
                                            view: window,
                                            bubbles: true,
                                            cancelable: true,
                                            clientX: centerX,
                                            clientY: centerY
                                        });
                                        item.dispatchEvent(event);
                                    });

                                    targetFound = true;
                                    logUserAction(`使用模拟点击成功`);
                                } catch (e3) {
                                    // 尝试方法3：使用HTMLElement.click()
                                    try {
                                        const clickableElement = item.querySelector('a') || item;
                                        clickableElement.click();
                                        targetFound = true;
                                        logUserAction(`使用HTMLElement.click()成功`);
                                    } catch (e4) {
                                        logUserAction(`所有点击方法都失败: ${e4.message}`);
                                    }
                                }
                            }
                        }

                        if (targetFound) {
                            break;
                        }
                    }
                }

                if (!targetFound) {
                    throw new Error(`未找到目标公共主页: ${targetPageName}`);
                }

                // 等待页面切换完成
                await new Promise(resolve => setTimeout(resolve, 500));

                // 更新索引
                const nextIndex = (currentPageIndex + 1) % savedPageNames.length;
                currentPageIndex = nextIndex;
                localStorage.setItem('fb_current_page_index', currentPageIndex.toString());

                logUserAction(`✅ 超级快速切换完成，索引已更新为: ${nextIndex}`);
                resolve();

            } catch (error) {
                logUserAction(`❌ 切换过程中出错: ${error.message}`);
                reject(error);
            } finally {
                // 清理：如果页面列表仍然打开，点击body关闭它
                document.body.click();
            }
        });
    }

    // =============== UI相关函数 ===============

    // 创建控制面板
    function createControlPanel() {
        // 主面板
        const panel = document.createElement('div');
        panel.style.cssText = `
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 9999;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            max-width: 320px;
            font-family: Arial, sans-serif;
            transition: all 0.3s ease;
        `;

        // 标题栏
        const titleBar = document.createElement('div');
        titleBar.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        `;

        const title = document.createElement('div');
        title.textContent = 'Facebook 自动工具';
        title.style.cssText = `
            font-weight: bold;
            font-size: 16px;
            color: #1877f2;
        `;

        // 最小化按钮
        const minimizeBtn = document.createElement('button');
        minimizeBtn.innerHTML = '−';
        minimizeBtn.style.cssText = `
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #666;
            padding: 0 5px;
            line-height: 1;
        `;

        let isMinimized = false;
        const content = document.createElement('div');
        content.style.transition = 'all 0.3s ease';

        minimizeBtn.onclick = () => {
            isMinimized = !isMinimized;
            content.style.display = isMinimized ? 'none' : 'block';
            minimizeBtn.innerHTML = isMinimized ? '+' : '−';
            panel.style.width = isMinimized ? 'auto' : '320px';
        };

        titleBar.appendChild(title);
        titleBar.appendChild(minimizeBtn);
        panel.appendChild(titleBar);

        // 创建标签页容器
        const tabContainer = document.createElement('div');
        tabContainer.style.cssText = `
            margin-bottom: 15px;
            display: flex;
            gap: 10px;
        `;

        // 创建标签页按钮
        function createTabButton(text, isActive = false) {
            const button = document.createElement('button');
            button.textContent = text;
            button.style.cssText = `
                padding: 8px 16px;
                border: none;
                border-radius: 6px;
                background: ${isActive ? '#1877f2' : '#f0f2f5'};
                color: ${isActive ? 'white' : '#1877f2'};
                cursor: pointer;
                font-size: 14px;
                transition: all 0.3s;
            `;
            return button;
        }

        // 创建标签页内容容器
        const tabContent = document.createElement('div');

        // 创建自动点赞标签页
        const autoLikeTab = createTabButton('自动点赞', true);
        const pageSwitchTab = createTabButton('页面切换', false);

        // 自动点赞内容
        const autoLikeContent = document.createElement('div');
        autoLikeContent.innerHTML = createAutoLikeContent();

        // 页面切换内容
        const pageSwitchContent = document.createElement('div');
        pageSwitchContent.innerHTML = createPageSwitchContent();
        pageSwitchContent.style.display = 'none';

        // 标签页切换逻辑
        autoLikeTab.onclick = () => {
            autoLikeTab.style.background = '#1877f2';
            autoLikeTab.style.color = 'white';
            pageSwitchTab.style.background = '#f0f2f5';
            pageSwitchTab.style.color = '#1877f2';
            autoLikeContent.style.display = 'block';
            pageSwitchContent.style.display = 'none';
        };

        pageSwitchTab.onclick = () => {
            pageSwitchTab.style.background = '#1877f2';
            pageSwitchTab.style.color = 'white';
            autoLikeTab.style.background = '#f0f2f5';
            autoLikeTab.style.color = '#1877f2';
            autoLikeContent.style.display = 'none';
            pageSwitchContent.style.display = 'block';
        };

        // 组装面板
        tabContainer.appendChild(autoLikeTab);
        tabContainer.appendChild(pageSwitchTab);
        content.appendChild(tabContainer);
        content.appendChild(autoLikeContent);
        content.appendChild(pageSwitchContent);
        panel.appendChild(content);

        // 添加到页面
        document.body.appendChild(panel);

        // 初始化显示
        updatePendingList();
    }

    // 创建自动点赞内容
    function createAutoLikeContent() {
        return `
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #444;">批量添加链接</label>
                <textarea id="linkInput" placeholder="输入 Facebook 链接（每行一个）" style="
                    width: 100%;
                    height: 80px;
                    padding: 8px;
                    margin-bottom: 10px;
                    border: 1px solid #ddd;
                    border-radius: 6px;
                    resize: vertical;
                    font-size: 14px;
                    box-sizing: border-box;
                "></textarea>
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #444;">待处理链接</label>
                <div id="pendingList" style="
                    max-height: 150px;
                    overflow-y: auto;
                    padding: 8px;
                    border: 1px solid #ddd;
                    border-radius: 6px;
                    background: #fff;
                    font-size: 13px;
                "></div>
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #444;">账号信息</label>
                <div id="accountInfo" style="
                    padding: 8px;
                    border: 1px solid #ddd;
                    border-radius: 6px;
                    background: #e8f4fd;
                    font-size: 12px;
                    min-height: 20px;
                    color: #333;
                ">正在获取账号信息...</div>
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #444;">当前状态</label>
                <div id="currentStatus" style="
                    padding: 8px;
                    border: 1px solid #ddd;
                    border-radius: 6px;
                    background: #f8f9fa;
                    font-size: 13px;
                    min-height: 20px;
                    color: #666;
                ">等待开始...</div>
            </div>

            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #444;">操作日志</label>
                <div id="userLogPanel" style="
                    height: 120px;
                    overflow-y: auto;
                    padding: 8px;
                    border: 1px solid #ddd;
                    border-radius: 6px;
                    background: #fff;
                    font-size: 13px;
                "></div>
            </div>

            <div style="
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
                margin-bottom: 10px;
            ">
                <button id="startAutoLikeBtn" style="
                    padding: 8px 15px;
                    border: none;
                    border-radius: 6px;
                    background: #1877f2;
                    color: white;
                    cursor: pointer;
                    font-size: 14px;
                    transition: background 0.3s;
                ">开始点赞</button>

                <button id="stopAutoLikeBtn" style="
                    padding: 8px 15px;
                    border: none;
                    border-radius: 6px;
                    background: #dc3545;
                    color: white;
                    cursor: pointer;
                    font-size: 14px;
                    transition: background 0.3s;
                ">停止点赞</button>

                <button id="skipCurrentBtn" style="
                    padding: 8px 15px;
                    border: none;
                    border-radius: 6px;
                    background: #ffc107;
                    color: #212529;
                    cursor: pointer;
                    font-size: 14px;
                    transition: background 0.3s;
                    opacity: 0.6;
                " disabled>跳过当前</button>

                <button id="addLinksBtn" style="
                    padding: 8px 15px;
                    border: none;
                    border-radius: 6px;
                    background: #28a745;
                    color: white;
                    cursor: pointer;
                    font-size: 14px;
                    transition: background 0.3s;
                ">添加链接</button>
            </div>

            <div style="
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                gap: 8px;
                margin-bottom: 10px;
            ">
                <button id="forceMarkBtn" style="
                    padding: 8px 15px;
                    border: none;
                    border-radius: 6px;
                    background: #17a2b8;
                    color: white;
                    cursor: pointer;
                    font-size: 14px;
                    transition: background 0.3s;
                    opacity: 0.6;
                " disabled>强制标记</button>

                <button id="clearLogsBtn" style="
                    padding: 8px 15px;
                    border: none;
                    border-radius: 6px;
                    background: #6c757d;
                    color: white;
                    cursor: pointer;
                    font-size: 14px;
                    transition: background 0.3s;
                ">清除日志</button>
            </div>
        `;
    }

    // 创建页面切换内容
    function createPageSwitchContent() {
        return `
            <div style="margin-bottom: 15px;">
                <label style="display: block; margin-bottom: 5px; font-weight: 500; color: #444;">页面切换日志</label>
                <div id="pageSwitchLogPanel" style="
                    height: 200px;
                    overflow-y: auto;
                    padding: 8px;
                    border: 1px solid #ddd;
                    border-radius: 6px;
                    background: #fff;
                    font-size: 13px;
                "></div>
            </div>

            <button id="performSwitchBtn" style="
                width: 100%;
                padding: 10px;
                border: none;
                border-radius: 6px;
                background: #1877f2;
                color: white;
                cursor: pointer;
                font-size: 14px;
                transition: background 0.3s;
                margin-bottom: 10px;
            ">一键切换页面</button>
        `;
    }

    // =============== 全局函数导出 ===============

    // 暴露必要的函数到全局作用域
    window.startAutoLike = startAutoLike;
    window.stopAutoLike = stopAutoLike;
    window.skipCurrentLink = skipCurrentLink;
    window.forceMarkAsLiked = forceMarkAsLiked;
    window.clearLogs = clearLogs;
    window.addLinks = () => {
        const input = document.querySelector('#linkInput');
        if (input && addToLikeList(input.value)) {
            const urls = parseFacebookUrls(input.value);
            showToast(`成功添加 ${urls.length} 个链接！`);
            input.value = '';
            updatePendingList();
        }
    };
    window.performSuperFastSwitch = performSuperFastSwitch;

    // 调试函数 - 检查存储状态
    window.debugStorage = () => {
        const globalLinks = JSON.parse(localStorage.getItem('fb_auto_like_posts_global') || '[]');
        const accountLiked = JSON.parse(localStorage.getItem('fb_liked_posts_by_account') || '{}');
        const currentAccount = getCurrentAccountName();

        console.log('=== 存储状态调试 ===');
        console.log('当前账号:', currentAccount);
        console.log('全局待处理链接:', globalLinks);
        console.log('所有账号已点赞记录:', accountLiked);
        console.log('当前账号已点赞:', accountLiked[currentAccount] || []);
        console.log('内存中待处理链接:', targetPosts);
        console.log('内存中已点赞链接:', likedPosts);

        logUserAction(`调试信息 - 当前账号: ${currentAccount}, 全局待处理: ${globalLinks.length}, 当前账号已点赞: ${(accountLiked[currentAccount] || []).length}`);

        return {
            currentAccount,
            globalLinks,
            accountLiked,
            currentAccountLiked: accountLiked[currentAccount] || [],
            memoryTargetPosts: targetPosts,
            memoryLikedPosts: likedPosts
        };
    };

    // 手动刷新数据函数
    window.refreshData = () => {
        refreshCurrentAccountData();
        logUserAction('手动刷新数据完成');
        showToast('数据已刷新', 'success');
    };

    // =============== 初始化 ===============

    // 添加必要的动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);

    // 添加账号信息更新函数
    function updateAccountInfo() {
        const accountInfoElement = document.querySelector('#accountInfo');
        if (accountInfoElement) {
            const currentAccount = getCurrentAccountName();
            const pendingCount = getPendingPosts().length;
            const likedCount = likedPosts.length;
            const totalCount = targetPosts.length;

            accountInfoElement.innerHTML = `
                <div style="font-size: 12px; color: #666; margin-bottom: 5px;">
                    <strong>当前账号:</strong> ${currentAccount}
                </div>
                <div style="font-size: 12px; color: #666;">
                    <strong>进度:</strong> ${likedCount}/${totalCount} 已点赞，${pendingCount} 待处理
                </div>
            `;
        }
    }

    // 初始化
    window.addEventListener('load', function() {
        // 重新初始化内存中的数组 - 使用新的存储机制
        const storedTargetPosts = JSON.parse(localStorage.getItem('fb_auto_like_posts_global') || '[]');
        const currentAccountLikedPosts = getCurrentAccountLikedPosts();

        // 清空并重新填充数组
        targetPosts.length = 0;
        likedPosts.length = 0;

        storedTargetPosts.forEach(url => targetPosts.push(url));
        currentAccountLikedPosts.forEach(url => likedPosts.push(url));

        console.log('初始化内存数组 - 全局待点赞:', targetPosts);
        console.log('初始化内存数组 - 当前账号已点赞:', likedPosts);
        console.log('当前账号:', getCurrentAccountName());

        // 创建统一的控制面板
        createControlPanel();

        // 初始化账号信息显示
        updateAccountInfo();

        // 绑定按钮事件
        const startAutoLikeBtn = document.getElementById('startAutoLikeBtn');
        const stopAutoLikeBtn = document.getElementById('stopAutoLikeBtn');
        const skipCurrentBtn = document.getElementById('skipCurrentBtn');
        const forceMarkBtn = document.getElementById('forceMarkBtn');
        const clearLogsBtn = document.getElementById('clearLogsBtn');
        const addLinksBtn = document.getElementById('addLinksBtn');
        const performSwitchBtn = document.getElementById('performSwitchBtn');

        if (startAutoLikeBtn) startAutoLikeBtn.addEventListener('click', window.startAutoLike);
        if (stopAutoLikeBtn) stopAutoLikeBtn.addEventListener('click', window.stopAutoLike);
        if (skipCurrentBtn) skipCurrentBtn.addEventListener('click', window.skipCurrentLink);
        if (forceMarkBtn) forceMarkBtn.addEventListener('click', window.forceMarkAsLiked);
        if (clearLogsBtn) clearLogsBtn.addEventListener('click', window.clearLogs);
        if (addLinksBtn) addLinksBtn.addEventListener('click', window.addLinks);
        if (performSwitchBtn) performSwitchBtn.addEventListener('click', window.performSuperFastSwitch);

        // 恢复自动点赞状态 - 增强版
        if (isRunning) {
            logUserAction('恢复自动点赞任务');
            updateCurrentStatus('🔄 恢复自动点赞任务...', 'processing');

            // 检查是否有待处理的URL
            const pendingCheckUrl = localStorage.getItem('fb_pending_check_url');
            const redirectTimestamp = parseInt(localStorage.getItem('fb_redirect_timestamp') || '0');
            const currentTime = Date.now();

            // 如果是刚跳转过来的页面（5秒内），并且有待检查的URL
            if (pendingCheckUrl && (currentTime - redirectTimestamp < 5000)) {
                const currentUrl = window.location.href;

                // 检查当前页面是否是目标页面
                if (isUrlSimilar(currentUrl, pendingCheckUrl)) {
                    logUserAction(`检测到页面跳转完成，开始处理: ${pendingCheckUrl}`);
                    updateCurrentStatus('🔄 页面跳转完成，开始处理...', 'processing');

                    // 清除待检查标记
                    localStorage.removeItem('fb_pending_check_url');
                    localStorage.removeItem('fb_redirect_timestamp');

                    // 设置当前处理的URL
                    currentOriginalUrl = pendingCheckUrl;

                    // 等待页面完全加载后开始检查
                    setTimeout(() => {
                        if (isRunning) {
                            checkAndLike(pendingCheckUrl);
                        }
                    }, 3000);
                } else {
                    logUserAction(`页面跳转异常，当前页面与目标不符`);
                    logUserAction(`当前: ${currentUrl}`);
                    logUserAction(`目标: ${pendingCheckUrl}`);

                    // 清除异常状态，重新开始
                    localStorage.removeItem('fb_pending_check_url');
                    localStorage.removeItem('fb_redirect_timestamp');
                    currentOriginalUrl = '';
                    currentFinalUrl = '';
                    saveCurrentState(true, '', '');

                    setTimeout(() => {
                        if (isRunning) {
                            performLike();
                        }
                    }, 2000);
                }
            } else {
                // 正常恢复流程
                if (currentOriginalUrl) {
                    logUserAction(`继续处理当前链接: ${currentOriginalUrl}`);
                    checkAndLike(currentOriginalUrl);
                } else {
                    logUserAction('没有当前处理链接，重新开始');
                    performLike();
                }
            }
        } else {
            updateCurrentStatus('等待开始...', 'info');
        }
    }

    // 检查URL是否相似（用于判断跳转是否成功）
    function isUrlSimilar(url1, url2) {
        // 移除参数和锚点
        const clean1 = url1.split('?')[0].split('#')[0];
        const clean2 = url2.split('?')[0].split('#')[0];

        // 直接比较
        if (clean1 === clean2) return true;

        // 提取帖子ID比较
        const id1 = extractPostId(clean1);
        const id2 = extractPostId(clean2);

        if (id1 && id2 && id1 === id2) return true;

        // 检查是否包含相同的关键部分
        const parts1 = clean1.split('/').filter(p => p && p.length > 3);
        const parts2 = clean2.split('/').filter(p => p && p.length > 3);

        // 如果有共同的长部分，认为是相似的
        for (const part1 of parts1) {
            for (const part2 of parts2) {
                if (part1 === part2 && part1.length > 10) {
                    return true;
                }
            }
        }

        return false;
    }

    // 提取帖子ID
    function extractPostId(url) {
        const patterns = [
            /\/posts\/(\d+)/,
            /\/(\d+)\/posts\/(\d+)/,
            /story_fbid=(\d+)/,
            /pfbid=([^&\/]+)/,
            /\/photo\.php\?fbid=(\d+)/,
            /\/videos\/(\d+)/
        ];

        for (const pattern of patterns) {
            const match = url.match(pattern);
            if (match) {
                return match[1] || match[2];
            }
        }
        return null;

        // 恢复页面切换状态
        const savedIndex = localStorage.getItem('fb_current_page_index');
        if (savedIndex !== null) {
            currentPageIndex = parseInt(savedIndex, 10);
        }

        const savedNames = localStorage.getItem('fb_saved_page_names');
        if (savedNames) {
            savedPageNames = JSON.parse(savedNames);
            logUserAction(`恢复了 ${savedPageNames.length} 个保存的公共主页名称`);
        }

        console.log('Facebook Auto Tools 已启动');
    });

})();
